server.port=9014
server.servlet.context-path=/moye
server.servlet.session.timeout=1d
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=1GB
spring.servlet.multipart.max-request-size=1GB

#登陆认证相关配置
#是否开启验证码
com.trs.security.enable-code=true
com.trs.security.login-fail-num-limit=5
com.trs.security.warn-update-pwd-days=365
com.trs.security.checksum-enable-config=false

#flyway
spring.flyway.baseline-on-migrate=true
spring.flyway.clean-disabled=true
spring.flyway.enabled=true

#logging configuration
logging.file.path=./logs

#mybatis-plus configuration
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

etcd.node.url=trs-etcd-svc.trs-moye-v4:2379
etcd.user.name=root
etcd.user.password=wzwxbszt2021!@#

model.xxl-job.realtime-cron=0 */5 * * * ?

kerberos.dir.path=/TRS/data/moye
kerberos.dir.name=proof

#是否开启moye应用初始化Runner
moye.initializer.enable=true

com.trs.security.jwt-secret-key=b506c5088ce617d73444fbabc5c2b030924b53baa1f88025eb6544745783f675
#登录token过期时间
com.trs.security.token-expiration-millis=302400000
com.trs.security.redis-token-expiration-millis=18000000

#ck历史数据清理天数
ck.data.retention.day=15

#spark配置，启动任务用默认值
spark.driver-cores=2
spark.driver-memory=1G
spark.executor-memory=1G
spark.executor-instances=2
spark.executor-cores=2
spark.cores-max=4

# 批处理日志保留天数
batch.task.monitor.retention.day=30

# 服务健康检测用seatunnel配置
seatunnel.server.url=seatunnel-svc:5801
seatunnel.server.workers=3
environment=演示环境

# feign请求超时时间，默认30分钟
feign.request.timeout-minutes=30

feign.client.config.default.readTimeout=3000000

stream.pipeline-test.default-fetch-message-count=10

xxl.job.executor.app=MOYE_BACKEND

auth.code.expire.time=60

