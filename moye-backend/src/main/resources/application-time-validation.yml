# 指标库导入时间验证配置
indicator:
  import:
    time-validation:
      # 是否启用时间验证
      enabled: true
      # 默认验证模式（STRICT/LENIENT/SMART）
      default-mode: SMART
      # 严格模式阈值（0-1）
      strict-threshold: 0.99
      # 宽松模式阈值（0-1）
      lenient-threshold: 0.5
      # 时间字段名称模式
      time-field-patterns:
        - bjjs
        - 时间
        - 日期
        - time
        - date
        - datetime
        - timestamp
        - 开始时间
        - 结束时间
        - 开始
        - 结束
        - 统计时间
        - 数据时间
        - 记录时间
      # 是否自动修正时间
      auto-correct-time: true