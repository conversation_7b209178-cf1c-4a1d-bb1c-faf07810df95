package com.trs.ai.moye.data.model.service.impl;

import com.trs.ai.moye.data.model.config.IndicatorImportConfig;
import com.trs.moye.base.mcp.SearchableField;
import com.trs.moye.base.mcp.TableInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * IndicatorImportService 时间校验功能测试
 * 
 * <AUTHOR>
 * @since 2025/8/27
 */
@ExtendWith(MockitoExtension.class)
class IndicatorImportServiceTest {

    @Mock
    private IndicatorImportConfig indicatorImportConfig;

    @InjectMocks
    private IndicatorImportService indicatorImportService;

    private TableInfo createTestTableInfo(LocalDateTime beginTime, LocalDateTime endTime) {
        TableInfo tableInfo = new TableInfo();
        tableInfo.setZhName("测试文件");
        
        // 创建字段列表
        List<SearchableField> fields = new ArrayList<>();
        SearchableField bjsjField = new SearchableField();
        bjsjField.setEnName("bjsj");
        bjsjField.setZhName("报警时间");
        fields.add(bjsjField);
        tableInfo.setFields(fields);
        
        // 创建测试数据
        List<Map<String, Object>> data = new ArrayList<>();
        
        // 添加开始时间的数据
        Map<String, Object> row1 = new HashMap<>();
        row1.put("bjsj", beginTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        data.add(row1);
        
        // 添加结束时间的数据
        Map<String, Object> row2 = new HashMap<>();
        row2.put("bjsj", endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        data.add(row2);
        
        // 添加中间时间的数据
        Map<String, Object> row3 = new HashMap<>();
        row3.put("bjsj", beginTime.plusHours(8).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        data.add(row3);
        
        tableInfo.setData(data);
        return tableInfo;
    }

    @Test
    void testParseFileTimeRange() {
        // 准备测试数据
        LocalDateTime fileBeginTime = LocalDateTime.of(2025, 8, 16, 0, 0, 0);
        LocalDateTime fileEndTime = LocalDateTime.of(2025, 8, 17, 0, 0, 0);
        TableInfo tableInfo = createTestTableInfo(fileBeginTime, fileEndTime);
        
        // 执行测试
        var result = indicatorImportService.parseFileTimeRange(tableInfo);
        
        // 验证结果
        assertEquals(fileBeginTime, result.getBeginTime());
        assertEquals(fileEndTime, result.getEndTime());
    }

    @Test
    void testValidateAndFixTimeRange_WithTolerance() {
        // 准备测试数据
        LocalDateTime inputBeginTime = LocalDateTime.of(2025, 8, 15, 16, 0, 0);
        LocalDateTime inputEndTime = LocalDateTime.of(2025, 8, 16, 16, 0, 0);
        LocalDateTime fileBeginTime = LocalDateTime.of(2025, 8, 15, 16, 30, 0);
        LocalDateTime fileEndTime = LocalDateTime.of(2025, 8, 16, 15, 30, 0);
        
        // 设置配置：容错1小时
        when(indicatorImportConfig.getTimeToleranceHours()).thenReturn(1);
        when(indicatorImportConfig.isAutoFixEnabled()).thenReturn(true);
        
        // 执行测试
        var result = indicatorImportService.validateAndFixTimeRange(
            inputBeginTime, inputEndTime, fileBeginTime, fileEndTime);
        
        // 验证结果：在容错范围内，应该通过
        assertTrue(result.isValid());
        assertEquals("时间范围一致", result.getMessage());
    }

    @Test
    void testValidateAndFixTimeRange_WithAutoFix() {
        // 准备测试数据：文件时间在传入时间范围内
        LocalDateTime inputBeginTime = LocalDateTime.of(2025, 8, 15, 16, 0, 0);
        LocalDateTime inputEndTime = LocalDateTime.of(2025, 8, 16, 16, 0, 0);
        LocalDateTime fileBeginTime = LocalDateTime.of(2025, 8, 15, 20, 0, 0);
        LocalDateTime fileEndTime = LocalDateTime.of(2025, 8, 16, 10, 0, 0);
        
        // 设置配置：启用自动修正
        when(indicatorImportConfig.getTimeToleranceHours()).thenReturn(1);
        when(indicatorImportConfig.isAutoFixEnabled()).thenReturn(true);
        
        // 执行测试
        var result = indicatorImportService.validateAndFixTimeRange(
            inputBeginTime, inputEndTime, fileBeginTime, fileEndTime);
        
        // 验证结果：应该自动修正为文件时间
        assertTrue(result.isValid());
        assertEquals(fileBeginTime, result.getActualBeginTime());
        assertEquals(fileEndTime, result.getActualEndTime());
        assertTrue(result.getMessage().contains("已自动使用文件实际时间范围"));
    }

    @Test
    void testValidateAndFixTimeRange_WithoutAutoFix() {
        // 准备测试数据：文件时间在传入时间范围内
        LocalDateTime inputBeginTime = LocalDateTime.of(2025, 8, 15, 16, 0, 0);
        LocalDateTime inputEndTime = LocalDateTime.of(2025, 8, 16, 16, 0, 0);
        LocalDateTime fileBeginTime = LocalDateTime.of(2025, 8, 15, 20, 0, 0);
        LocalDateTime fileEndTime = LocalDateTime.of(2025, 8, 16, 10, 0, 0);
        
        // 设置配置：不启用自动修正
        when(indicatorImportConfig.getTimeToleranceHours()).thenReturn(1);
        when(indicatorImportConfig.isAutoFixEnabled()).thenReturn(false);
        
        // 执行测试
        var result = indicatorImportService.validateAndFixTimeRange(
            inputBeginTime, inputEndTime, fileBeginTime, fileEndTime);
        
        // 验证结果：应该返回失败
        assertFalse(result.isValid());
        assertTrue(result.getMessage().contains("时间范围不一致"));
    }

    @Test
    void testParseDateTime_VariousFormats() {
        // 测试各种时间格式
        assertDoesNotThrow(() -> indicatorImportService.parseDateTime("2025-08-16 10:30:00"));
        assertDoesNotThrow(() -> indicatorImportService.parseDateTime("2025-08-16 10:30"));
        assertDoesNotThrow(() -> indicatorImportService.parseDateTime("2025/08/16 10:30:00"));
        assertDoesNotThrow(() -> indicatorImportService.parseDateTime("2025-08-16"));
        
        // 测试Excel数字格式
        assertDoesNotThrow(() -> indicatorImportService.parseDateTime("45878.4375")); // 2025-08-16 10:30
    }
}